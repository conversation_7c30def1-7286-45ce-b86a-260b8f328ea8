

-- DPD delivery method
INSERT INTO `delivery_method` (`externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(NULL, 'DPD', 'Kurýr DPD', '', '<p><span>Zásilky odeslané přes DPD dorazí adresátovi do druhého dne po odeslání. DPD nabízí rychlé a spolehlivé doručení přímo na adresu. O expedici objednávky Vás informuje DPD prostřednictvím e-mailové a SMS zprávy, je proto zkontrolovat správnost kontaktních údajů. Stejným způsobem budete informování o doručování z<PERSON>. Pokud zásilku odmítnete převzít, DPD ji vrací zpět na náš sklad. V košíku najdete i možnost doručení na Slovensko.</span></p>', NULL, 2, 1, 0, 1, 1, 1, 1, 2, '{"shop":"14:00","supplier_store":"14:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527107"]}]}');

SET @dpd_delivery_method_id = LAST_INSERT_ID();

-- DPD delivery method currencies
INSERT INTO `delivery_method_currency` (`deliveryMethodId`, `currency`) VALUES
	(@dpd_delivery_method_id, 'CZK');
INSERT INTO `delivery_method_currency` (`deliveryMethodId`, `currency`) VALUES
	(@dpd_delivery_method_id, 'EUR');

-- DPD delivery method price
INSERT INTO `delivery_method_price` (`externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(NULL, @dpd_delivery_method_id, 1, 1, 119.0000, 'CZK', 1299.0000, 31500, 50000);

-- DPD delivery method x payment method relationships
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(@dpd_delivery_method_id, 1); -- DPD + Bank Transfer
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(@dpd_delivery_method_id, 2); -- DPD + Cash on Delivery
-- DPD delivery method x state relationship
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(@dpd_delivery_method_id, 1); -- DPD available in state 1
