<?php

declare(strict_types=1);

namespace App\Api\V1\Forms\Contact;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\Api\V1\Forms\BaseFormController;
use App\FrontModule\Components\ContactForm\ContactFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\PostType\Page\Model\Orm\TreeRepository;

#[Path('/')]
#[Tag('noAuthentication'), Tag('Form')]
final class ContactFormController extends BaseFormController
{
	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly MutationsHolder $mutationsHolder,
		private readonly TreeRepository $treeRepository,
		private readonly ContactFormFactory $contactFormFactory,
	) {}

	#[Path('/contact')]
	#[Method('POST')]
	#[Response(description: 'Success', code: '204')]
	#[Response(description: 'Form data invalid', code: '422')]
	public function submit(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($mutation);

		$root = $this->treeRepository->getBy([
			'parent' => null,
			'mutation' => $mutation,
		]);

		if ($root === null) {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}

		$form = $this->contactFormFactory->create($root, standalone: true);
		$form->allowCrossOrigin();

		$this->fakeSubmit($form);
		$form->fireEvents();

		if ($form->isSuccess()) {
			return $response->withStatus(ApiResponse::S204_NO_CONTENT);
		}

		return $this->jsonResponse(['errors' => $form->getErrors()], $response)
			->withStatus(ApiResponse::S422_UNPROCESSABLE_ENTITY);
	}
}
