<?php

declare(strict_types=1);

namespace App\Api\V1\Forms;

use Apitte\Core\Annotation\Controller\Path;
use App\Api\V1\Controllers\BaseV1Controller;
use Nette\Forms\Form;
use Nette\Forms\SubmitterControl;

#[Path('/form')]
abstract class BaseFormController extends BaseV1Controller
{
	protected function fakeSubmit(Form $form): void
	{
		foreach ($form->getControls() as $control) {
			if ($control instanceof SubmitterControl) {
				$control->setValue('1');
			}
		}
	}
}
