<?php declare(strict_types = 1);

namespace App\Api\V1\Newsletter;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Pages\Detail\Response\Page;
use App\Exceptions\UserException;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use Nette\Utils\Validators;
use Nextras\Orm\Entity\IEntity;
use Throwable;

#[Path('/newsletter')]
#[Tag('Newsletter')]
final class NewsletterController extends BaseV1Controller
{
	private Mutation $mutation;
	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly NewsletterEmailModel $newsletterEmailModel,
		private readonly Orm $orm,
	) {
		$this->mutation = $this->mutationsHolder->getDefault();
		$this->orm->setMutation($this->mutation);
	}

	#[Path('/subscribe')]
	#[Method('POST')]
	public function subscribe(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		try {
			$rawData = $request->getJsonBody();

			$email = $rawData['email'];

			if ( ! Validators::isEmail($email)) {
				throw new ClientErrorException();
			}

			$subscribed = $this->newsletterEmailModel->subscribeEmail($email, $this->mutation);
			$this->orm->flush();
		} catch (UserException $e) {
			return $this->jsonResponse(['state' => 'error', 'error' => $e->getMessage()], $response->withStatus(404));
		} catch (Throwable){
			return $this->jsonResponse(['state' => 'error', 'error' => 'Invalid e-mail address.'], $response->withStatus(404));
		}

		return $this->jsonResponse(
			[
				'state' => 'ok',
				'email' => $rawData['email'],
				'result' => $subscribed instanceof IEntity,
			],
			$response,
		);
	}

	#[Path('/unsubscribe')]
	#[Method('POST')]
	public function unsubscribe(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		try {
			$rawData = $request->getJsonBody();

			$email = $rawData['email'];

			if (!Validators::isEmail($email)) {
				throw new ClientErrorException();
			}

			$unsubscribed = $this->newsletterEmailModel->unsubscribeEmail($email, $this->mutation);
			$this->orm->flush();

		} catch (Throwable $e){
			return $this->jsonResponse(['state' => 'error', 'error' => 'Invalid e-mail address.'], $response->withStatus(404));
		}

		return $this->jsonResponse(
			[
				'state' => 'ok',
				'email' => $rawData['email'],
				'result' => $unsubscribed,
			],
			$response,
		);
	}
}
