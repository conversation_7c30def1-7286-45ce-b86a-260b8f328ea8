<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Entity\Response\CategoryList;
use App\Api\Entity\Response\PickupPointList;
use App\Model\CacheFactory;
use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette\Caching\Cache;
use Nextras\Dbal\Connection;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Category')]
final class CategoryController extends BaseV1Controller
{
	private Mutation $mutation;
	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly CacheFactory $cacheFactory,
	)
	{
		$this->mutation = $this->orm->mutation->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setPublicOnly(false);
	}

	#[Path('/categories')]
	#[Method('GET')]
	#[Response(description: 'Success', code: '200')]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$responseArray = $this->cacheFactory->create('api')->load(self::class, function (&$dependencies) {
			$dependencies[Cache::Expire] = '2 hour';
			$dependencies[Cache::Tags] = ['category', 'api'];
			$categories = [$this->mutation->pages->eshop];
			$this->createList($this->mutation->pages->eshop->id, $categories);

			$responseEntity = new CategoryList($categories);

			return $responseEntity->toResponse();
		});


		return $this->jsonResponse(['state' => 'ok'] + $responseArray, $response);
	}

	private function createList(int $parent, array &$categories): void
	{
		$result = $this->orm->tree->findBy(['parent' => $parent])->orderBy('sort');
		foreach ($result as $category) {
			$categories[$category->id] = $category;
			$this->createList($category->id, $categories);
		}
	}
}
