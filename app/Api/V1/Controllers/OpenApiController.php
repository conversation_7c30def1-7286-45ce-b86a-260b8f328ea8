<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\OpenApi;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\UI\Controller\IController;
use Apitte\OpenApi\ISchemaBuilder;
use Psr\Http\Message\ResponseInterface;

#[Path('/api/v1')]
#[Tag('OpenApi')]
#[Tag('noAuthentication')]
final class OpenApiController implements IController
{

	public function __construct(
		private readonly ISchemaBuilder $schemaBuilder,
	)
	{
	}

	#[OpenApi("summary: Get OpenAPI definition.")]
	#[Path("/openapi")]
	#[Method("GET")]
	public function index(ApiRequest $request, ApiResponse $response): ResponseInterface
	{
		return $response
			->withAddedHeader('Access-Control-Allow-Origin', '*')
			->writeJsonBody(
				$this->schemaBuilder->build()->toArray()
			);
	}

}
