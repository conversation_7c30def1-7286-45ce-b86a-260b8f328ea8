<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\Api\Entity\Response\MutationList;
use App\Model\Mutation\MutationsHolder;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Mutation')]
final class MutationListController extends BaseV1Controller
{

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}

	#[Path('/mutation')]
	#[Method('GET')]
	#[Response(description: 'Success', code: '200', entity: MutationList::class)]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$responseEntity = new MutationList(
			$this->mutationsHolder->findAll()
		);

		return $this->jsonResponse($responseEntity->toResponse(), $response);
	}

}
