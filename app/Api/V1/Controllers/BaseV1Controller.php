<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\UI\Controller\IController;

#[Path('/api/v1')]
abstract class BaseV1Controller implements IController
{

	protected function jsonResponse(array $responseBody, ApiResponse $response): ApiResponse
	{
		return $response
			->withHeader('Cache-Control', 'no-cache')
			->withHeader('Expires', '0')
			->withHeader('Pragma', 'no-cache')
			->writeJsonBody($responseBody);
	}

	protected function plainResponse(string $responseBody, ApiResponse $response): ApiResponse
	{
		return $response
			->withHeader('Cache-Control', 'no-cache')
			->withHeader('Expires', '0')
			->withHeader('Pragma', 'no-cache')
			->withHeader('Content-Type', 'text/plain')
			->writeBody($responseBody);
	}

	protected function redirectResponse(string $location, ApiResponse $response): ApiResponse
	{
		return $response
			->withHeader('Cache-Control', 'no-cache')
		    ->withHeader('Expires', '0')
		    ->withHeader('Pragma', 'no-cache')
		    ->withHeader('Location', $location)
		    ->withStatus(302);
	}

}
