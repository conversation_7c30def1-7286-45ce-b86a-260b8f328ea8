<?php

declare(strict_types=1);

namespace App\Api\Entity\Response;

use JsonSerializable;
use stdClass;
use function explode;

final class CustomContent implements JsonSerializable
{
	public function __construct(
		private readonly stdClass $content,
	) {}

	public static function from(stdClass|null $content): self|null
	{
		return $content !== null
			? new self($content)
			: null;
	}

	public function jsonSerialize(): mixed
	{
		$result = [];
		// @phpstan-ignore-next-line
		foreach ($this->content as $key => $value) {
			[$type, $id] = explode('____', $key, 2);
			$result[] = [
				'id' => $id,
				'type' => $type,
				'data' => new CustomFields($value[0]),
			];
		}

		return $result;
	}
}
