<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Model\Orm\Mutation\Mutation;

class MutationList extends BasicEntity
{

	/** @var MutationListItem[] */
	public readonly array $mutations;

	public function __construct(
		array $mutations
	)
	{
		$mutationListItems = [];
		foreach ($mutations as $item) {
			assert($item instanceof Mutation);
			$mutationListItems[] = new MutationListItem($item);
		}

		$this->mutations = $mutationListItems;
	}

}
